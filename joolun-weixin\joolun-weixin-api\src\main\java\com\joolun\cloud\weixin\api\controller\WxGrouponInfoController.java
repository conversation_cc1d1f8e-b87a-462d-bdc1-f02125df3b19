package com.joolun.cloud.weixin.api.controller;

import com.joolun.cloud.common.core.util.R;
import com.joolun.cloud.weixin.api.annotation.ApiLogin;
import com.joolun.cloud.weixin.api.service.GrouponAndOrderService;
import com.joolun.cloud.weixin.api.service.WxGrouponInfoService;
import com.joolun.cloud.weixin.api.util.ThirdSessionHolder;
import com.joolun.cloud.weixin.common.constant.MyReturnCode;
import com.joolun.cloud.weixin.common.dto.WxGrouponInfoDTO;
import com.joolun.cloud.weixin.common.entity.ThirdSession;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 拼团活动
 *
 * <AUTHOR>
 * @date 2021-09-08 19:23:44
 */
@Slf4j
@RestController
@AllArgsConstructor	
@RequestMapping("/wxspellgroup")
@Api(value = "wxspellgroup", tags = "拼团活动管理")
public class WxGrouponInfoController {

	private final WxGrouponInfoService wxGrouponInfoService;

	private final GrouponAndOrderService grouponAndOrderService;


	/**
	 * 拼团活动查询
	 * @param wxGrouponInfoDTO
	 * @return R
	 */
	@ApiOperation(value = "拼团活动查询")
	@GetMapping("/info")
	@ApiLogin
	public R getInfoById(WxGrouponInfoDTO wxGrouponInfoDTO) {

		ThirdSession thirdSession = ThirdSessionHolder.getThirdSession();
		if (Objects.isNull(thirdSession)|| Objects.isNull(thirdSession.getWxUserId())) {
			return R.failed(MyReturnCode.ERR_60001.getCode(), MyReturnCode.ERR_60001.getMsg());
		}
		return grouponAndOrderService.userGetByPageId(wxGrouponInfoDTO);
	}

	/**
	 * 拼团活动查询
	 * @param wxGrouponInfoDTO
	 * @return R
	 */
	@ApiOperation(value = "拼团活动查询")
	@GetMapping("/freedom/info")
	@ApiLogin
	public R getInfoByIdFreedom(WxGrouponInfoDTO wxGrouponInfoDTO) {
		return grouponAndOrderService.userGetByPageId(wxGrouponInfoDTO);
	}

	/**
	 * 参加拼团活动
	 * @param wxGrouponInfoDTO
	 * @return R
	 */
	@ApiOperation(value = "参加拼团活动")
	@PostMapping("/join")
	@ApiLogin
	public R join(@RequestBody WxGrouponInfoDTO wxGrouponInfoDTO) {
		ThirdSession thirdSession = ThirdSessionHolder.getThirdSession();
		if (Objects.isNull(thirdSession)|| Objects.isNull(thirdSession.getWxUserId())) {
			return R.failed(MyReturnCode.ERR_60001.getCode(), MyReturnCode.ERR_60001.getMsg());
		}
		return grouponAndOrderService.completeGroup(wxGrouponInfoDTO);
	}

	/**
	 * 拿取已购用户信息
	 * @return R
	 */
	@ApiOperation(value = "参加拼团活动")
	@GetMapping("/orderuser/{id}")
	@ApiLogin
	public R getUserInfo(@PathVariable String id) {
		return R.ok(wxGrouponInfoService.getPurchasedUser(id));
	}

	/**
	 * 拿取进入用户/已购用户数量
	 * @return R
	 */
	@ApiOperation(value = "拿取进入用户/已购用户数量")
	@GetMapping("/usernum/{id}")
	@ApiLogin
	public R getUserNum(@PathVariable String id) {
		return R.ok(grouponAndOrderService.getUserNum(id));
	}


}